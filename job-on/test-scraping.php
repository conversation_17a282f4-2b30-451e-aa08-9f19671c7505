<?php

/**
 * Test script to verify the Google Maps scraping integration
 * This script tests the scraping functionality without database dependencies
 */

require_once __DIR__ . '/vendor/autoload.php';

use Symfony\Component\Process\Process;
use Symfony\Component\Process\Exception\ProcessFailedException;

class ScrapingTest
{
    public function testGoogleMapsScraper()
    {
        echo "Testing Google Maps Scraper Integration...\n";
        
        try {
            // Test the JavaScript scraper
            $result = $this->runGoogleMapsScraper('cleaning services near 10001', 'cleaning', 3);
            
            if ($result['success']) {
                echo "✅ Scraper test PASSED\n";
                echo "Found " . count($result['results']['businesses']) . " businesses:\n";
                
                foreach ($result['results']['businesses'] as $index => $business) {
                    echo "  " . ($index + 1) . ". " . $business['name'] . "\n";
                    echo "     Address: " . $business['address'] . "\n";
                    echo "     Phone: " . $business['phone'] . "\n";
                    echo "     Category: " . $business['category'] . "\n";
                    echo "\n";
                }
                
                return true;
            } else {
                echo "❌ Scraper test FAILED\n";
                echo "Error: " . $result['error']['message'] . "\n";
                return false;
            }
            
        } catch (Exception $e) {
            echo "❌ Scraper test FAILED with exception\n";
            echo "Error: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    protected function runGoogleMapsScraper(string $locationQuery, string $categoryQuery, int $maxResults = 15): array
    {
        // Get the absolute path to the scraper script
        $scriptPath = __DIR__ . '/laravel-scraper.js';
        
        if (!file_exists($scriptPath)) {
            throw new Exception('Google Maps scraper script not found at: ' . $scriptPath);
        }

        // Build search query for cleaning businesses
        $searchQuery = $categoryQuery && strtolower($categoryQuery) !== 'general' 
            ? "$categoryQuery services near $locationQuery"
            : "cleaning services near $locationQuery";

        $command = [
            'node',
            $scriptPath,
            $searchQuery,
            $locationQuery, // landmark/location
            (string) $maxResults // max results
        ];

        echo "Running command: " . implode(' ', $command) . "\n";

        $process = new Process($command);
        $process->setTimeout(60); // 1 minute timeout
        $process->setWorkingDirectory(__DIR__); // Set working directory
        
        try {
            $process->mustRun();
            $output = $process->getOutput();
            
            echo "Scraper output length: " . strlen($output) . " characters\n";
            
            $result = json_decode($output, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('Invalid JSON response from Google Maps scraper: ' . json_last_error_msg());
            }
            
            return $result;
            
        } catch (ProcessFailedException $exception) {
            $errorOutput = $process->getErrorOutput();
            
            echo "Scraper process failed:\n";
            echo "Command: " . implode(' ', $command) . "\n";
            echo "Error output: " . $errorOutput . "\n";
            echo "Exit code: " . $process->getExitCode() . "\n";
            
            // Try to parse error output as JSON
            $errorData = json_decode($errorOutput, true);
            
            throw new Exception(
                $errorData['error']['message'] ?? 'Google Maps scraper process failed: ' . $errorOutput
            );
        }
    }
    
    public function testBusinessDataStructure()
    {
        echo "\nTesting business data structure...\n";
        
        $mockBusiness = [
            'name' => 'Test Cleaning Service',
            'address' => '123 Test St, Test City, NY 10001',
            'phone' => '******-123-4567',
            'website' => 'https://testcleaning.com',
            'rating' => 4.5,
            'category' => 'Cleaning Services',
            'services' => ['House Cleaning', 'Office Cleaning'],
            'hours' => null,
            'photos' => [],
            'reviews' => 25,
            'lat' => null,
            'lng' => null
        ];
        
        $requiredFields = ['name', 'address', 'phone', 'category'];
        $missingFields = [];
        
        foreach ($requiredFields as $field) {
            if (!isset($mockBusiness[$field]) || empty($mockBusiness[$field])) {
                $missingFields[] = $field;
            }
        }
        
        if (empty($missingFields)) {
            echo "✅ Business data structure test PASSED\n";
            echo "All required fields present: " . implode(', ', $requiredFields) . "\n";
            return true;
        } else {
            echo "❌ Business data structure test FAILED\n";
            echo "Missing fields: " . implode(', ', $missingFields) . "\n";
            return false;
        }
    }
}

// Run the tests
echo "=== Google Maps Scraping Integration Test ===\n\n";

$test = new ScrapingTest();

$scraperTest = $test->testGoogleMapsScraper();
$structureTest = $test->testBusinessDataStructure();

echo "\n=== Test Results ===\n";
echo "Scraper Test: " . ($scraperTest ? "PASSED" : "FAILED") . "\n";
echo "Structure Test: " . ($structureTest ? "PASSED" : "FAILED") . "\n";

if ($scraperTest && $structureTest) {
    echo "\n🎉 All tests PASSED! The scraping integration is working correctly.\n";
    exit(0);
} else {
    echo "\n❌ Some tests FAILED. Please check the implementation.\n";
    exit(1);
}
