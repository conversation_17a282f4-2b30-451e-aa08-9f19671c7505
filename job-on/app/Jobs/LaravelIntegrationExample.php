<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\Process\Process;
use Symfony\Component\Process\Exception\ProcessFailedException;

class ScrapeBusiness implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $location;
    protected $landmark;
    protected $maxResults;
    protected $cacheKey;

    public function __construct($location, $landmark = null, $maxResults = 15)
    {
        $this->location = $location;
        $this->landmark = $landmark;
        $this->maxResults = $maxResults;
        $this->cacheKey = 'businesses_' . md5($location . '_' . $landmark);
    }

    public function handle()
    {
        try {
            Log::info("Starting business scraping job", [
                'location' => $this->location,
                'landmark' => $this->landmark,
                'max_results' => $this->maxResults
            ]);

            // Step 1: Check cache first (fastest)
            $cachedBusinesses = $this->getCachedBusinesses();
            if ($cachedBusinesses) {
                Log::info("Found cached businesses", [
                    'location' => $this->location,
                    'count' => count($cachedBusinesses),
                    'source' => 'cache'
                ]);
                return $cachedBusinesses;
            }

            // Step 2: Search in existing database
            $existingBusinesses = $this->searchExistingBusinesses();
            if ($existingBusinesses->isNotEmpty()) {
                Log::info("Found existing businesses in database", [
                    'location' => $this->location,
                    'count' => $existingBusinesses->count(),
                    'source' => 'database'
                ]);
                
                // Cache the results for future requests
                $businessArray = $existingBusinesses->toArray();
                $this->cacheBusinesses($businessArray);
                
                return $businessArray;
            }

            // Step 3: Run scraper as last resort
            Log::info("No existing businesses found, running scraper", [
                'location' => $this->location,
                'landmark' => $this->landmark,
                'source' => 'scraper'
            ]);

            $scrapedData = $this->runScraper();
            
            if ($scrapedData['success'] && !empty($scrapedData['results']['businesses'])) {
                $businesses = $scrapedData['results']['businesses'];
                
                // Save to database
                $this->saveBusinessesToDatabase($businesses);
                
                // Cache the results
                $this->cacheBusinesses($businesses);
                
                Log::info("Successfully scraped and saved businesses", [
                    'location' => $this->location,
                    'count' => count($businesses),
                    'source' => 'scraper'
                ]);
                
                return $businesses;
            } else {
                $errorMessage = $scrapedData['error']['message'] ?? 'No businesses found';
                Log::warning("Scraping completed but no results", [
                    'location' => $this->location,
                    'error' => $errorMessage
                ]);
                
                return [];
            }

        } catch (\Exception $e) {
            Log::error("Business scraping job failed", [
                'location' => $this->location,
                'landmark' => $this->landmark,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Return empty array instead of throwing to prevent job failure
            return [];
        }
    }

    private function getCachedBusinesses()
    {
        return Cache::get($this->cacheKey);
    }

    private function cacheBusinesses($businesses, $minutes = 60)
    {
        Cache::put($this->cacheKey, $businesses, now()->addMinutes($minutes));
    }

    private function searchExistingBusinesses()
    {
        // Search existing business database with improved logic
        $query = \App\Models\Business::query();
        
        // Parse location for better matching
        $locationParts = $this->parseLocation($this->location);
        
        // Search by various location components
        $query->where(function ($q) use ($locationParts) {
            foreach ($locationParts as $part) {
                $q->orWhere('location', 'LIKE', '%' . $part . '%')
                  ->orWhere('address', 'LIKE', '%' . $part . '%')
                  ->orWhere('city', 'LIKE', '%' . $part . '%')
                  ->orWhere('state', 'LIKE', '%' . $part . '%');
            }
        });

        // Add landmark filtering if provided
        if ($this->landmark) {
            $query->where(function ($q) {
                $q->where('address', 'LIKE', '%' . $this->landmark . '%')
                  ->orWhere('landmark', 'LIKE', '%' . $this->landmark . '%')
                  ->orWhere('name', 'LIKE', '%' . $this->landmark . '%');
            });
        }

        // Only get recent entries (within last 30 days) to ensure freshness
        $query->where('scraped_at', '>', now()->subDays(30));
        
        return $query->limit($this->maxResults)->get();
    }

    private function parseLocation($location)
    {
        // Extract meaningful parts from location string
        $parts = [];
        
        // Remove common prefixes/suffixes
        $cleaned = preg_replace('/^(US-|United States)/i', '', $location);
        
        // Split by common delimiters
        $segments = preg_split('/[,\s]+/', $cleaned);
        
        foreach ($segments as $segment) {
            $segment = trim($segment);
            if (strlen($segment) > 2) {
                $parts[] = $segment;
            }
        }
        
        return array_unique($parts);
    }

    private function runScraper()
    {
        // Get the absolute path to the scraper script
        $scriptPath = base_path('laravel-scraper.js');
        
        // Fallback paths if script is in different location
        if (!file_exists($scriptPath)) {
            $scriptPath = base_path('scripts/laravel-scraper.js');
        }
        if (!file_exists($scriptPath)) {
            $scriptPath = resource_path('js/laravel-scraper.js');
        }
        
        if (!file_exists($scriptPath)) {
            throw new \Exception('Scraper script not found. Please ensure laravel-scraper.js is in the project root.');
        }

        $command = [
            'node',
            $scriptPath,
            $this->location
        ];

        if ($this->landmark) {
            $command[] = $this->landmark;
        }
        
        $command[] = (string) $this->maxResults;

        Log::info("Running scraper command", [
            'command' => implode(' ', $command),
            'script_path' => $scriptPath
        ]);

        $process = new Process($command);
        $process->setTimeout(180); // 3 minutes timeout for better reliability
        $process->setWorkingDirectory(base_path()); // Set working directory
        
        try {
            $process->mustRun();
            $output = $process->getOutput();
            
            Log::info("Scraper output received", [
                'output_length' => strlen($output),
                'location' => $this->location
            ]);
            
            $result = json_decode($output, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Invalid JSON response from scraper: ' . json_last_error_msg());
            }
            
            return $result;
            
        } catch (ProcessFailedException $exception) {
            $errorOutput = $process->getErrorOutput();
            
            Log::error("Scraper process failed", [
                'command' => implode(' ', $command),
                'error_output' => $errorOutput,
                'exit_code' => $process->getExitCode()
            ]);
            
            // Try to parse error output as JSON
            $errorData = json_decode($errorOutput, true);
            
            throw new \Exception(
                $errorData['error']['message'] ?? 'Scraper process failed: ' . $errorOutput
            );
        }
    }

    private function saveBusinessesToDatabase($businesses)
    {
        $savedCount = 0;
        
        foreach ($businesses as $businessData) {
            try {
                $business = \App\Models\Business::updateOrCreate(
                    [
                        'name' => $businessData['name'],
                        'phone' => $businessData['phone'] ?? null,
                    ],
                    [
                        'address' => $businessData['address'] ?? '',
                        'website' => $businessData['website'] ?? null,
                        'rating' => $businessData['rating'] ?? null,
                        'reviews' => $businessData['reviews'] ?? null,
                        'category' => $businessData['category'] ?? 'General Business',
                        'location' => $this->location,
                        'landmark' => $this->landmark,
                        'scraped_at' => now(),
                        'data_source' => 'google_maps_scraper'
                    ]
                );
                
                $savedCount++;
                
            } catch (\Exception $e) {
                Log::warning("Failed to save business to database", [
                    'business_name' => $businessData['name'] ?? 'Unknown',
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        Log::info("Saved businesses to database", [
            'total_businesses' => count($businesses),
            'saved_count' => $savedCount,
            'location' => $this->location
        ]);
    }
}

// Usage example in your controller:
/*
class BusinessController extends Controller
{
    public function searchBusinesses(Request $request)
    {
        $location = $request->input('location');
        $landmark = $request->input('landmark');
        $maxResults = $request->input('max_results', 15);

        // Dispatch the job
        $job = new ScrapeBusiness($location, $landmark, $maxResults);
        
        // For immediate execution (synchronous)
        $businesses = $job->handle();
        
        // Or for queue execution (asynchronous)
        // dispatch($job);
        
        return response()->json([
            'success' => true,
            'businesses' => $businesses
        ]);
    }
}
*/