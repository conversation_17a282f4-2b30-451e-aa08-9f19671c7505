#!/usr/bin/env node

/**
 * Google Maps Business Scraper for Laravel Integration
 *
 * This script scrapes cleaning businesses from Google Maps
 * and returns the data in JSON format for Laravel processing.
 *
 * Usage: node laravel-scraper.js "cleaning services near 12345" "12345" 15
 */

const puppeteer = require("puppeteer");

class GoogleMapsScraper {
  constructor() {
    this.browser = null;
    this.page = null;
  }

  async init() {
    try {
      this.browser = await puppeteer.launch({
        headless: true,
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-dev-shm-usage",
          "--disable-accelerated-2d-canvas",
          "--no-first-run",
          "--no-zygote",
          "--single-process",
          "--disable-gpu",
        ],
      });
      this.page = await this.browser.newPage();

      // Set user agent to avoid detection
      await this.page.setUserAgent(
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
      );

      // Set viewport
      await this.page.setViewport({ width: 1366, height: 768 });
    } catch (error) {
      throw new Error(`Failed to initialize browser: ${error.message}`);
    }
  }

  async scrapeBusinesses(searchQuery, maxResults = 15) {
    try {
      // For now, return mock data to test the integration
      console.error(`Mock scraping for: ${searchQuery}`);

      const mockBusinesses = [
        {
          name: "ABC Cleaning Services",
          address: "123 Main St, New York, NY 10001",
          phone: "******-123-4567",
          website: "https://abccleaning.com",
          rating: 4.5,
          category: "Cleaning Services",
          services: ["House Cleaning", "Office Cleaning"],
          hours: null,
          photos: [],
          reviews: 25,
          lat: null,
          lng: null,
        },
        {
          name: "Professional Cleaners NYC",
          address: "456 Broadway, New York, NY 10001",
          phone: "******-987-6543",
          website: "https://procleanersnyc.com",
          rating: 4.8,
          category: "Cleaning Services",
          services: ["Deep Cleaning", "Move-in Cleaning"],
          hours: null,
          photos: [],
          reviews: 42,
          lat: null,
          lng: null,
        },
      ];

      return {
        success: true,
        results: {
          businesses: mockBusinesses.slice(0, maxResults),
          total: Math.min(mockBusinesses.length, maxResults),
          search_query: searchQuery,
        },
      };
    } catch (error) {
      console.error(`Scraping error: ${error.message}`);
      return {
        success: false,
        error: {
          message: error.message,
          search_query: searchQuery,
        },
      };
    }
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);

  if (args.length < 1) {
    console.error(
      JSON.stringify({
        success: false,
        error: {
          message:
            'Usage: node laravel-scraper.js "search query" [location] [max_results]',
        },
      })
    );
    process.exit(1);
  }

  const searchQuery = args[0];
  const maxResults = parseInt(args[2]) || 15;

  const scraper = new GoogleMapsScraper();

  try {
    await scraper.init();
    const result = await scraper.scrapeBusinesses(searchQuery, maxResults);
    console.log(JSON.stringify(result, null, 2));
  } catch (error) {
    console.error(
      JSON.stringify({
        success: false,
        error: {
          message: error.message,
        },
      })
    );
    process.exit(1);
  } finally {
    await scraper.close();
  }
}

if (require.main === module) {
  main();
}

module.exports = GoogleMapsScraper;
